package com.lz.account.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 原始凭证对象 lz_original_voucher
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "原始凭证")
@TableName(value = "lz_original_voucher")
@Data
public class OriginalVoucher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /** 原始凭证文件分类表ID */
    @Excel(name = "原始凭证文件分类表ID")
    @TableField("base_financial_doc_category_id")
    @ApiModelProperty(value = "原始凭证文件分类表ID")
    private Long baseFinancialDocCategoryId;

    /** 原始凭证模版表ID */
    @Excel(name = "原始凭证模版表ID")
    @TableField("invoice_voucher_template_id")
    @ApiModelProperty(value = "原始凭证模版表ID")
    private Long invoiceVoucherTemplateId;

    /** 系统存储表ID */
    @Excel(name = "系统存储表ID")
    private Long storageId;

    /** 是否已生成记账凭证（0：未生成，1：已生成） */
    @Excel(name = "是否已生成记账凭证", readConverterExp = "0=：未生成，1：已生成")
    @TableField("voucher_generated_flag")
    @ApiModelProperty(value = "是否已生成记账凭证（0：未生成，1：已生成）")
    private String voucherGeneratedFlag;

    /** 金额 */
    @Excel(name = "金额")
    @TableField("amount")
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /** 记账凭证表ID */
    @Excel(name = "记账凭证表ID")
    @TableField("voucher_id")
    @ApiModelProperty(value = "记账凭证表ID")
    private String voucherId;

    /** 原始凭证用途表ID */
    @Excel(name = "原始凭证用途表ID")
    @TableField("purpose_id")
    @ApiModelProperty(value = "原始凭证用途表ID")
    private Long purposeId;

    /** 记账期间(格式：YYYYMM) */
    @Excel(name = "记账期间(格式：YYYYMM)")
    @TableField("accounting_period")
    @ApiModelProperty(value = "记账期间(格式：YYYYMM)")
    private String accountingPeriod;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0:未删除 1:已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0:未删除 1:已删除）")
    private String delFlag;

}
