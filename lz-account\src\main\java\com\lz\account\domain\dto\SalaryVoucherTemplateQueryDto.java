package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 薪资类型查询参数dto
 */
@ApiModel(description = "薪资类型查询参数dto")
@Data
public class SalaryVoucherTemplateQueryDto {
    /**
     * 凭证类型（1：计提工资，2：发放工资）
     */
    @ApiModelProperty(value = "凭证类型（1：计提工资，2：发放工资）")
    private String voucherType;

    /**
     * 薪资类型
     */
    @ApiModelProperty(value = "薪资类型")
    private String salaryType;

    /**
     * 状态（0：禁用，1：启用）
     */
    @ApiModelProperty(value = "状态（0：禁用，1：启用）")
    private String status;
}
