package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lz.account.domain.entity.HrEmployee;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 小程序端员工基本信息修改参数
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("小程序端员工基本信息修改参数")
public class EmployeeBasicInfoUpdateDto {

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;


    /** 姓名 */
    @Excel(name = "姓名")
    @TableField("name")
    @ApiModelProperty(value = "姓名")
    private String name;

    /** 性别 */
    @Excel(name = "性别")
    @TableField("gender")
    @ApiModelProperty(value = "性别")
    private String gender;

    /** 证件类型 */
    @Excel(name = "证件类型")
    @TableField("id_type")
    @ApiModelProperty(value = "证件类型")
    private String idType;

    /** 证件号码 */
    @Excel(name = "证件号码")
    @TableField("id_number")
    @ApiModelProperty(value = "证件号码")
    private String idNumber;

    /** 人员状态（1:正常 0:非正常） */
    @Excel(name = "人员状态", readConverterExp = "1=:正常,0=:非正常")
    @TableField("status")
    @ApiModelProperty(value = "人员状态（1:正常 0:非正常）")
    private String status;
}
