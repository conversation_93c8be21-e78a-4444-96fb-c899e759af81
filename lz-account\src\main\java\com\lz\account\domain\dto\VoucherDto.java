package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel(description = "一键生成凭证参数Dto")
public class VoucherDto {
    @ApiModelProperty(value = "凭证字",required = true)
    private String word;
    @ApiModelProperty(value = "附单据数量",required = true)
    private Integer receipt_num;
    @ApiModelProperty(value = "凭证年份")
    private Integer voucherYear;
    @ApiModelProperty(value = "凭证月份")
    private Integer voucherMonth;
    @ApiModelProperty(value = "凭证日期")
    private Date voucherDate;
    @ApiModelProperty(value = "是否结转损益",required = true)
    private Boolean carryForward;
}
