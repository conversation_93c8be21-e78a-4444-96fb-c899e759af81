package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lz.account.domain.entity.BkStatementsVoucherTemplate;
import com.lz.account.domain.entity.BkStatementsVoucherTemplateDetails;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 *  根据模板id查询模板详情数据Dto
 * <AUTHOR>
 * @date 2025-05-14
 */

@Data
@ApiModel("根据模板id查询模板详情数据Dto")
public class StatementVoucherDetailDto {
    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 银行流水凭证模版表ID */
    @Excel(name = "银行流水凭证模版表ID")
    @TableField("statements_voucher_template_id")
    @ApiModelProperty(value = "银行流水凭证模版表ID")
    private Long statementsVoucherTemplateId;

    /** 摘要 */
    @Excel(name = "摘要")
    @TableField("abstract_text")
    @ApiModelProperty(value = "摘要")
    private String abstractText;

    /** 科目ID */
    @Excel(name = "科目ID")
    @TableField("subject_id")
    @ApiModelProperty(value = "科目ID")
    private Long subjectId;

    /** 借贷方向（1：借，2：贷） */
    @Excel(name = "借贷方向", readConverterExp = "1=：借，2：贷")
    @TableField("direction")
    @ApiModelProperty(value = "借贷方向（1：借，2：贷）")
    private String direction;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;
}
