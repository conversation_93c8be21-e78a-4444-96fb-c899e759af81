package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lz.account.domain.entity.FaAsset;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
/**
 * 分页查询参数
 * <AUTHOR>
 * @date 2025-04-19
 */
@Data
@ApiModel("分页查询参数Dto")
public class FaAssetQueryDto {

    /** 资产编号（3位类别+5位序列） */
    @Excel(name = "资产编号", readConverterExp = "3=位类别+5位序列")
    @TableField("asset_number")
    @ApiModelProperty(value = "资产编号（3位类别+5位序列）")
    private String assetNumber;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @TableField("name")
    @ApiModelProperty(value = "资产名称")
    private String name;

    /** 资产类别ID */
    @Excel(name = "资产类别ID")
    @TableField("category_id")
    @ApiModelProperty(value = "资产类别ID")
    private Long categoryId;

    /** 开始使用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始使用日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("start_use_date")
    @ApiModelProperty(value = "开始使用日期开始")
    private Date startUseDateBegin;

    /** 开始使用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始使用日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("start_use_date")
    @ApiModelProperty(value = "开始使用日期结束")
    private Date startUseDateEnd;

    /** 开始计提期间（YYYYMM） */
    @Excel(name = "开始计提期间", readConverterExp = "Y=YYYMM")
    @TableField("start_depreciation_period")
    @ApiModelProperty(value = "开始计提期间（YYYYMM）开始")
    private String startDepreciationPeriodBegin;

    /** 开始计提期间（YYYYMM） */
    @Excel(name = "开始计提期间", readConverterExp = "Y=YYYMM")
    @TableField("start_depreciation_period")
    @ApiModelProperty(value = "开始计提期间（YYYYMM）结束")
    private String startDepreciationPeriodEnd;

    @ApiModelProperty(value = "折旧年限")
    private Integer depreciationYears;

    @ApiModelProperty(value = "清理状态")
    private String disposalStatus;
}
