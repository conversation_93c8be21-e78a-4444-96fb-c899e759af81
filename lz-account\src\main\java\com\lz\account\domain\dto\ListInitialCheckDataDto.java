package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
/**
 * 期初检查数据库返回参数
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@ApiModel("期初检查参数Dto")
public class ListInitialCheckDataDto {

    /**
     * 借方金额
     */
    @ApiModelProperty(value = "借方金额")
    private BigDecimal debitAmount;

    /**
     * 贷方金额
     */
    @ApiModelProperty(value = "贷方金额")
    private BigDecimal creditAmount;
}
