package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 获取发票对应的凭证模板存储对象
 * <AUTHOR>
 * @date 2025-06-07
 */
@Data
@ApiModel("发票对应凭证模板存储对象")
public class InvoiceTemplateDto {

    /** 发票id */
    private Long invoiceId;

    /** 模板id */
    private Long templateId;

    /** 模板名称 */
    private String templateName;

    /** 是否系统默认 */
    private Long isDefault;
}
