package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 批量删除凭证参数
 */
@Data
@ApiModel(description = "批量删除凭证参数")
public class BatchDeleteVoucherDto {
    @ApiModelProperty(value = "凭证ID数组", example = "[1,2,3]")
    private Integer[] checked;
    @ApiModelProperty(value = "期间年", example = "2020")
    private Integer year;
    @ApiModelProperty(value = "期间月", example = "1")
    private Integer month;
    @ApiModelProperty(value = "账套ID", example = "1")
    private Long accountSetId;
}
