package com.lz.account.domain.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 待办状态对象 lz_todo_status
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@ApiModel(description = "待办状态")
@TableName(value = "lz_todo_status")
@Data
public class TodoStatus extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 记账期间（YYYYMM） */
    @Excel(name = "记账期间", readConverterExp = "Y=YYYMM")
    @TableField("posting_period")
    @ApiModelProperty(value = "记账期间（YYYYMM）")
    private String postingPeriod;

    /** 待办类型（1：公司成本，2：银行流水，3：公司发票） */
    @Excel(name = "待办类型", readConverterExp = "1=：公司成本，2：银行流水，3：公司发票")
    @TableField("todo_type")
    @ApiModelProperty(value = "待办类型（1：公司成本，2：银行流水，3：公司发票）")
    private String todoType;

    /** 代办状态（0：未完成，1：已完成) */
    @Excel(name = "代办状态", readConverterExp = "代办状态（0：未完成，1：已完成)")
    @TableField("todo_status")
    @ApiModelProperty(value = "代办状态（0：未完成，1：已完成)")
    private String todoStatus;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0：未删除，1：已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0：未删除，1：已删除）")
    private String delFlag;

}
