package com.lz.account.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 公司薪酬对象 hr_salary
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@ApiModel(description = "公司薪酬")
@TableName(value = "hr_salary")
@Data
public class HrSalary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 薪酬类型 */
    @Excel(name = "薪酬类型")
    @TableField("salary_type")
    @ApiModelProperty(value = "薪酬类型")
    private String salaryType;

    /** 记账期间（YYYYMM） */
    @Excel(name = "记账期间", readConverterExp = "Y=YYYMM")
    @TableField("posting_period")
    @ApiModelProperty(value = "记账期间（YYYYMM）")
    private String postingPeriod;

    /** 收入总额 */
    @Excel(name = "收入总额")
    @TableField("total_income")
    @ApiModelProperty(value = "收入总额")
    private BigDecimal totalIncome;

    /** 税款总额 */
    @Excel(name = "税款总额")
    @TableField("total_tax")
    @ApiModelProperty(value = "税款总额")
    private BigDecimal totalTax;

    /** 支付方式 */
    @Excel(name = "支付方式")
    @TableField("payment_method")
    @ApiModelProperty(value = "支付方式")
    private String paymentMethod;

    /** 计提凭证ID */
    @Excel(name = "计提凭证ID")
    @TableField("accrual_voucher_id")
    @ApiModelProperty(value = "计提凭证ID")
    private Long accrualVoucherId;

    /** 薪酬发放凭证ID */
    @Excel(name = "薪酬发放凭证ID")
    @TableField("payment_voucher_id")
    @ApiModelProperty(value = "薪酬发放凭证ID")
    private Long paymentVoucherId;

    /** 申报状态（1:未申报;3:申报失败;4:作废中;5:申报成功,无需缴款;6:申报成功,未缴款;7:已缴款;8:缴款中） */
    @Excel(name = "申报状态", readConverterExp = "1=:未申报;3:申报失败;4:作废中;5:申报成功,无需缴款;6:申报成功,未缴款;7:已缴款;8:缴款中")
    @TableField("decl_status")
    @ApiModelProperty(value = "申报状态（1:未申报;3:申报失败;4:作废中;5:申报成功,无需缴款;6:申报成功,未缴款;7:已缴款;8:缴款中）")
    private String declStatus;

    /** 申报失败原因 */
    @Excel(name = "申报失败原因")
    @TableField("decl_fail_reason")
    @ApiModelProperty(value = "申报失败原因")
    private String declFailReason;

    /** 扣款状态（0:未发起扣款;1:扣款成功;2:信息校验失败;3:无欠费记录;4:扣款失败） */
    @Excel(name = "扣款状态", readConverterExp = "0=:未发起扣款;1:扣款成功;2:信息校验失败;3:无欠费记录;4:扣款失败")
    @TableField("deduct_status")
    @ApiModelProperty(value = "扣款状态（0:未发起扣款;1:扣款成功;2:信息校验失败;3:无欠费记录;4:扣款失败）")
    private String deductStatus;

    /** 扣款失败原因 */
    @Excel(name = "扣款失败原因")
    @TableField("deduct_fail_reason")
    @ApiModelProperty(value = "扣款失败原因")
    private String deductFailReason;

    /** 实缴税额 */
    @Excel(name = "实缴税额")
    @TableField("actual_tax")
    @ApiModelProperty(value = "实缴税额")
    private BigDecimal actualTax;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0:未删除 1:已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0:未删除 1:已删除）")
    private String delFlag;

    /** 人数 */
    @Excel(name = "人数")
    @TableField("employee_count")
    @ApiModelProperty(value = "人数")
    private int employeeCount;

}
