package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: 添加员工信息dto
 */
@Data
@ApiModel(value = "添加员工信息dto")
public class EmployeeAddDto {
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "入职日期")
    private Date entryDate;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String idType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String idNumber;
    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    private String nationality;

    @ApiModelProperty(value = "是否残疾，0：否，1：是")
    private String disabled;

    @ApiModelProperty(value = "是否孤老，0：否，1：是")
    private String loneElderly;

    @ApiModelProperty(value = "是否烈士，0：否，1：是")
    private String martyrDescendant;

    @ApiModelProperty(value = "学历，1：研究生，2：大学本科，3：大学本科以下")
    private String educationLevel;

    /**
     * 任职类型
     */
    @ApiModelProperty(value = "任职类型")
    private String employmentType;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 户籍地址
     */
    @ApiModelProperty(value = "户籍地址")
    private String censusAddress;

    /**
     * 居住地址
     */
    @ApiModelProperty(value = "居住地址")
    private String residenceAddress;

}
