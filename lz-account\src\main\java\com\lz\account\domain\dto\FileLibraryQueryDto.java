package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: 文件库查询
 */
@ApiModel(description = "文件库查询")
@Data
public class FileLibraryQueryDto {
    @ApiModelProperty(value = "文件名称")
    private String name;
    @ApiModelProperty(value = "文件分类id")
    private Long docCategoryId;
    @ApiModelProperty(value = "创建时间-开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime addStartTime;
    @ApiModelProperty(value = "创建时间-结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime addEndTime;

}
