package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 编辑（新增/修改）薪资凭证模版dto
 */
@Data
@ApiModel(description = "编辑（新增/修改）薪资凭证模版dto")
public class EditSalVoucherTemplateDto {
    /**
     * ID
     */
    @ApiModelProperty(value = "模板表ID")
    private Long id;

    /**
     * 凭证类型（1：计提工资，2：发放工资）
     */
    @ApiModelProperty(value = "凭证类型（1：计提工资，2：发放工资）")
    private String voucherType;

    /**
     * 薪资类型
     */
    @ApiModelProperty(value = "薪资类型")
    private String salaryType;

    /**
     * 状态（0：禁用，1：启用）
     */
    @ApiModelProperty(value = "状态（0：禁用，1：启用）")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "模板明细")
    private List<EditTemplateDetailDto> templateDetails;


    @Data
    @ApiModel(description = "编辑模板明细dto")
    public static class EditTemplateDetailDto {
        /**
         * ID
         */
        @ApiModelProperty(value = "模板明细项表ID")
        private Long id;

        /**
         * 薪资凭证模版表ID
         */
        @ApiModelProperty(value = "薪资凭证模版表ID")
        private Long salaryVoucherTemplateId;

        /**
         * 摘要
         */
        @ApiModelProperty(value = "摘要")
        private String abstractText;

        /**
         * 科目ID
         */
        @ApiModelProperty(value = "科目ID")
        private Long subjectId;

        /**
         * 方向（1：借，2：贷）
         */
        @ApiModelProperty(value = "方向（1：借，2：贷）")
        private String direction;

        /**
         * 取值类型
         */
        @ApiModelProperty(value = "取值类型")
        private String valueType;
    }
}
