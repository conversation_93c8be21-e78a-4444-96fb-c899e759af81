package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lz.account.domain.entity.BkStatementsDetails;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

/**
 * 流水明细查询接口响应参数
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@ApiModel("流水明细查询参数dto")
public class StatementDetailsQueryDto {
    /** 银行流水表ID */
    @Excel(name = "银行流水表ID")
    @TableField("statements_id")
    @ApiModelProperty(value = "银行流水表ID", required = true)
    private Long statementsId;

    /** 收款人 */
    @Excel(name = "收款人")
    @TableField("payee")
    @ApiModelProperty(value = "收款人", required = false)
    private String payee;

    /** 付款人 */
    @Excel(name = "付款人")
    @TableField("payer")
    @ApiModelProperty(value = "付款人", required = false)
    private String payer;

    /** 开始查询交易时间 */
    @ApiModelProperty(value = "开始查询交易时间", required = false)
    private Date startTransactionTime;

    /** 终止查询交易时间 */
    @ApiModelProperty(value = "终止查询交易时间", required = false)
    private Date endTransactionTime;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注", required = false)
    private String remark;

    /** 生成银行流水凭证状态（0：未生成，1：已生成，2不入账） */
    @Excel(name = "生成银行流水凭证状态", readConverterExp = "0=：未生成，1：已生成，2不入账")
    @TableField("voucher_generated_status")
    @ApiModelProperty(value = "生成银行流水凭证状态（0：未生成，1：已生成，2不入账）", required = false)
    private String voucherGeneratedStatus;
}
