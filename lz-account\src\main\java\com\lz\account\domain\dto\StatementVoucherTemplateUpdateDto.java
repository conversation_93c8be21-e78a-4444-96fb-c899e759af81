package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lz.account.domain.entity.BkStatementsVoucherTemplate;
import com.lz.account.domain.entity.BkStatementsVoucherTemplateDetails;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 修改流水凭证模板接口响应参数
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@ApiModel("修改流水模板参数Dto")
public class StatementVoucherTemplateUpdateDto {
    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 模板名称 */
    @Excel(name = "模板名称")
    @TableField("name")
    @ApiModelProperty(value = "模板名称")
    private String name;

    /** 匹配字词（允许多个，英文逗号分隔） */
    @Excel(name = "匹配字词", readConverterExp = "允许多个，英文逗号分隔")
    @TableField("match_words")
    @ApiModelProperty(value = "匹配字词（允许多个，英文逗号分隔）")
    private List<String> matchWords;

    /** 支付方向（1：支出，2：收入） */
    @Excel(name = "支付方向", readConverterExp = "1=：支出，2：收入")
    @TableField("pay_direction")
    @ApiModelProperty(value = "支付方向（1：支出，2：收入）")
    private String payDirection;

    /** 匹配顺序（0~99 优先匹配最大的） */
    @Excel(name = "匹配顺序", readConverterExp = "0=~99,优=先匹配最大的")
    @TableField("match_order")
    @ApiModelProperty(value = "匹配顺序（0~99 优先匹配最大的）")
    private String matchOrder;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "流水凭证模板明细修改参数Dto")
    List<StatementVoucherDetailUpdateDto> statementVoucherDetailUpdateDtos;
}
