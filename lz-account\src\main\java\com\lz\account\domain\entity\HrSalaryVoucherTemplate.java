package com.lz.account.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 薪资凭证模版对象 hr_salary_voucher_template
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@ApiModel(description = "薪资凭证模版")
@TableName(value = "hr_salary_voucher_template")
@Data
public class HrSalaryVoucherTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 凭证类型（1：计提工资，2：发放工资） */
    @Excel(name = "凭证类型", readConverterExp = "1=：计提工资，2：发放工资")
    @TableField("voucher_type")
    @ApiModelProperty(value = "凭证类型（1：计提工资，2：发放工资）")
    private String voucherType;

    /** 薪资类型 */
    @Excel(name = "薪资类型")
    @TableField("salary_type")
    @ApiModelProperty(value = "薪资类型")
    private String salaryType;

    /** 状态（0：禁用，1：启用） */
    @Excel(name = "状态", readConverterExp = "0=：禁用，1：启用")
    @TableField("status")
    @ApiModelProperty(value = "状态（0：禁用，1：启用）")
    private String status;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0:未删除 1:已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0:未删除 1:已删除）")
    private String delFlag;

}
