package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 预览后生成流水凭证接收参数
 * <AUTHOR>
 * @date 2025-05-14
 */

@Data
@ApiModel("预览后生成流水凭证接收参数")
public class StatementVoucherDto {
    /**
     * 流水明细id
     */
    @ApiModelProperty(value = "流水明细id", example = "1")
    private Long detailId;
    /**
     * 凭证编号
     */
    @TableField(value = "code")
    @ApiModelProperty(value = "凭证编号", example = "1001")
    private Integer code;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注", example = "工资发放")
    private String remark;

    /**
     * 附单据数量
     */
    @TableField(value = "receipt_num")
    @ApiModelProperty(value = "附单据数量", example = "3")
    private Integer receiptNum;

    @TableField(exist = false)
    @ApiModelProperty(value = "凭证明细列表")
    private List<VoucherDetailsDto> details = new ArrayList<>(0);
}
