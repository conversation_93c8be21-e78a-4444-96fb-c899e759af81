package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 凭证辅助项关联对象 lz_voucher_details_auxiliary
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "凭证辅助项关联")
@TableName(value = "lz_voucher_details_auxiliary")
@Data
public class VoucherDetailsAuxiliary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;

    /** 凭证明细 Id */
    @Excel(name = "凭证明细 Id")
    @TableField("voucher_details_id")
    @ApiModelProperty(value = "凭证明细 Id")
    private Long voucherDetailsId;

    /** 辅助类型 id */
    @Excel(name = "辅助类型 id")
    @TableField("accounting_category_id")
    @ApiModelProperty(value = "辅助类型 id")
    private Long accountingCategoryId;

    /** 辅助项值 Id */
    @Excel(name = "辅助项值 Id")
    @TableField("accounting_category_details_id")
    @ApiModelProperty(value = "辅助项值 Id")
    private Long accountingCategoryDetailsId;

}
