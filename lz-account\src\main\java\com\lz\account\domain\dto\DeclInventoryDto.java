package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lz.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "申报清册")
@Data
public class DeclInventoryDto
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 账套名称 */
    @ApiModelProperty(value = "账套名称")
    private String companyName;

    /** 增值税纳税人类型（0：小规模纳税人:1：一般纳税人） */
    @ApiModelProperty(value = "增值税纳税人类型（0：小规模纳税人:1：一般纳税人）")
    private String taxpayerStatus;

    /** 税种 */
    @ApiModelProperty(value = "税种")
    private String taxesCategories;

    /** 所属账期yyyyMM */
    @ApiModelProperty(value = "所属账期yyyyMM")
    private String postingPeriod ;

    /** 取数状态（0：未取数，1：取数中，2：取数成功，3：取数异常） */
    @ApiModelProperty(value = "取数状态（0：未取数，1：取数中，2：取数成功，3：取数异常）")
    private List<String> fetchStatusList;

    /** 申报状态（0：未申报，1：申报中，2：申报成功，3：发现已完成申报，申报异常，4：申报异常） */
    @ApiModelProperty(value = "申报状态（0：未申报，1：申报中，2：申报成功，3：发现已完成申报，申报异常，4：申报异常）")
    private List<String> declStatusList;

    /** 缴款状态（0：未扣款，1：扣款中，2：扣款成功，3：无需扣款，4：发现已完成扣款，扣款异常，5：扣款失败） */
    @ApiModelProperty(value = "缴款状态（0：未扣款，1：扣款中，2：扣款成功，3：无需扣款，4：发现已完成扣款，扣款异常，5：扣款失败）")
    private List<String> payStatusList;
}
