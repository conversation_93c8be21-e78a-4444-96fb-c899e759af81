package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增流水明细接口响应参数
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@ApiModel("流水凭证模板明细新增参数Dto")
public class StatementVoucherDetailInsertDto {
    /** 摘要 */
    @Excel(name = "摘要")
    @TableField("abstract_text")
    @ApiModelProperty(value = "摘要")
    private String abstractText;

    /** 科目ID */
    @Excel(name = "科目ID")
    @TableField("subject_id")
    @ApiModelProperty(value = "科目ID")
    private Long subjectId;

    /** 借贷方向（1：借，2：贷） */
    @Excel(name = "借贷方向", readConverterExp = "1=：借，2：贷")
    @TableField("direction")
    @ApiModelProperty(value = "借贷方向（1：借，2：贷）")
    private String direction;
}
