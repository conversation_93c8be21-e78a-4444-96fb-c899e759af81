package com.lz.account.domain.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 记账凭证对象 lz_voucher
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "记账凭证")
@TableName(value = "lz_voucher")
@Data
public class Voucher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;

    /** 凭证字 */
    @Excel(name = "凭证字")
    @TableField("word")
    @ApiModelProperty(value = "凭证字")
    private String word;

    /** 凭证编号 */
    @Excel(name = "凭证编号")
    @TableField("code")
    @ApiModelProperty(value = "凭证编号")
    private Integer code;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 附单据数量 */
    @Excel(name = "附单据数量")
    @TableField("receipt_num")
    @ApiModelProperty(value = "附单据数量")
    private Integer receiptNum;

    /** 制单人 */
    @Excel(name = "制单人")
    @TableField("create_member")
    @ApiModelProperty(value = "制单人")
    private Long createMember;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /** 借方总金额 */
    @Excel(name = "借方总金额")
    @TableField("debit_amount")
    @ApiModelProperty(value = "借方总金额")
    private BigDecimal debitAmount;

    /** 贷方总金额 */
    @Excel(name = "贷方总金额")
    @TableField("credit_amount")
    @ApiModelProperty(value = "贷方总金额")
    private BigDecimal creditAmount;

    /** 所属账套id */
    @Excel(name = "所属账套id")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "所属账套id")
    private Long accountSetsId;

    /** 凭证年份 */
    @Excel(name = "凭证年份")
    @TableField("voucher_year")
    @ApiModelProperty(value = "凭证年份")
    private Integer voucherYear;

    /** 凭证月份 */
    @Excel(name = "凭证月份")
    @TableField("voucher_month")
    @ApiModelProperty(value = "凭证月份")
    private Integer voucherMonth;

    /** 凭证日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "凭证日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("voucher_date")
    @ApiModelProperty(value = "凭证日期")
    private Date voucherDate;

    /** 审核人 ID */
    @Excel(name = "审核人 ID")
    @TableField("audit_member_id")
    @ApiModelProperty(value = "审核人 ID")
    private Long auditMemberId;

    /** 审核人姓名 */
    @Excel(name = "审核人姓名")
    @TableField("audit_member_name")
    @ApiModelProperty(value = "审核人姓名")
    private String auditMemberName;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("audit_date")
    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    /** 是否结转损益 */
    @Excel(name = "是否结转损益")
    @TableField("carry_forward")
    @ApiModelProperty(value = "是否结转损益")
    private Boolean carryForward;

    /** 账单列表 */
    @Excel(name = "账单列表")
    @TableField("bill_list")
    @ApiModelProperty(value = "账单列表")
    private String billList;

    @TableField(exist = false)
    @ApiModelProperty(value = "凭证明细列表")
    private List<VoucherDetails> details = new ArrayList<>(0);

}
