package com.lz.account.domain.dto;

import com.lz.common.core.domain.entity.SysUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 原始凭证关联凭证
 */
@Data
@ApiModel(description = "原始凭证关联凭证dto")
public class RelateVoucherDto {
    @ApiModelProperty(value = "原始凭证表id数组")
    private List<Long> originalVoucherId;
    @ApiModelProperty(value = "记账凭证id数组")
    private Long voucherId;
}
