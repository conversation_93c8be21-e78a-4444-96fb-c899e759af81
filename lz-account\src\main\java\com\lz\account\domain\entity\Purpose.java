package com.lz.account.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 原始凭证用途对象 lz_purpose
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "原始凭证用途")
@TableName(value = "lz_purpose")
@Data
public class Purpose extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用途ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "用途ID")
    private Long id;

    /** 父级ID（为空表示一级分类） */
    @Excel(name = "父级ID", readConverterExp = "为=空表示一级分类")
    @TableField("parent_id")
    @ApiModelProperty(value = "父级ID（为空表示一级分类）")
    private Long parentId;

    /** 用途名称 */
    @Excel(name = "用途名称")
    @TableField("name")
    @ApiModelProperty(value = "用途名称")
    private String name;

    /** 匹配字词（允许多个，英文逗号分隔） */
    @Excel(name = "匹配字词", readConverterExp = "允许多个，英文逗号分隔")
    @TableField("match_words")
    @ApiModelProperty(value = "匹配字词（允许多个，英文逗号分隔）")
    private String matchWords;

    /** 匹配顺序（0~99 优先匹配最大的） */
    @Excel(name = "匹配顺序", readConverterExp = "0~99 优先匹配最大的")
    @TableField("match_order")
    @ApiModelProperty(value = "匹配顺序（0~99 优先匹配最大的）")
    private String matchOrder;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0:未删除 1:已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0:未删除 1:已删除）")
    private String delFlag;

}
