package com.lz.account.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 薪资凭证模版明细对象 hr_salary_voucher_template_detail
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@ApiModel(description = "薪资凭证模版明细")
@TableName(value = "hr_salary_voucher_template_detail")
@Data
public class HrSalaryVoucherTemplateDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 薪资凭证模版表ID */
    @Excel(name = "薪资凭证模版表ID")
    @TableField("salary_voucher_template_id")
    @ApiModelProperty(value = "薪资凭证模版表ID")
    private Long salaryVoucherTemplateId;

    /** 摘要 */
    @Excel(name = "摘要")
    @TableField("abstract_text")
    @ApiModelProperty(value = "摘要")
    private String abstractText;

    /** 科目ID */
    @Excel(name = "科目ID")
    @TableField("subject_id")
    @ApiModelProperty(value = "科目ID")
    private Long subjectId;

    /** 方向（1：借，2：贷） */
    @Excel(name = "方向", readConverterExp = "1=：借，2：贷")
    @TableField("direction")
    @ApiModelProperty(value = "方向（1：借，2：贷）")
    private String direction;

    /** 取值类型 */
    @Excel(name = "取值类型")
    @TableField("value_type")
    @ApiModelProperty(value = "取值类型")
    private String valueType;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0:未删除 1:已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0:未删除 1:已删除）")
    private String delFlag;

}
