package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "申报清册")
@Data
public class DeclInventoryUpdateDto
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 税种 */
    @ApiModelProperty(value = "税种")
    private String taxesCategories;

    /** 增值税纳税人类型（0：小规模纳税人:1：一般纳税人） */
    @ApiModelProperty(value = "增值税纳税人类型（0：小规模纳税人:1：一般纳税人）")
    private String taxpayerStatus;

    /** 税款属期起 */
    @ApiModelProperty(value = "税款属期起")
    private String fromDate;

    /** 税款属期止 */
    @ApiModelProperty(value = "税款属期止")
    private String toDate;

    /** 申报周期（5:次，4:月，3:季，2:半年，1:年） */
    @ApiModelProperty(value = "申报周期（5:次，4:月，3:季，2:半年，1:年）")
    private String cycleId;

    /** 完税证明文件ID */
    @ApiModelProperty(value = "完税证明文件ID")
    private Long wszmIncludeId;

    /** 已申报税种PDF文件ID */
    @ApiModelProperty(value = "已申报税种PDF文件ID")
    private Long reportedTaxTypeId;

}
