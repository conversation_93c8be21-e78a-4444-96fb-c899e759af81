package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 申报模版对象 decl_template
 *
 */
@ApiModel(description = "修改报表配置")
@Data
public class DeclTemplateUpdateDto
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 税种 */
    @ApiModelProperty(value = "税种")
    private String taxesCategories;

    /** 模版名称 */
    @ApiModelProperty(value = "模版名称")
    private String templateName;

    /** 模版描述 */
    @ApiModelProperty(value = "模版描述")
    private String templateRemark;

    /** 适用地区 */
    @ApiModelProperty(value = "适用地区")
    private String applicationZone;

}
