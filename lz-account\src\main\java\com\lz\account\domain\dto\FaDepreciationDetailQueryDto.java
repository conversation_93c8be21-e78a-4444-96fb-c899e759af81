package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lz.account.domain.entity.FaDepreciationDetail;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询折旧明细参数
 * <AUTHOR>
 * @date 2025-04-21
 */
@Data
@ApiModel("折旧明细表查询参数Dto")
public class FaDepreciationDetailQueryDto {
    /** 记账期间（yyyyMM） */
    @Excel(name = "记账期间", readConverterExp = "yyyyMM")
    @TableField("posting_period")
    @ApiModelProperty(value = "记账期间（yyyyMM）开始")
    private String postingPeriodBegin;

    /** 记账期间（yyyyMM） */
    @Excel(name = "记账期间", readConverterExp = "yyyyMM")
    @TableField("posting_period")
    @ApiModelProperty(value = "记账期间（yyyyMM）结束")
    private String postingPeriodEnd;

    /** 记账期间（yyyyMM） */
    @Excel(name = "记账期间", readConverterExp = "yyyyMM")
    @TableField("posting_period")
    @ApiModelProperty(value = "记账期间（yyyyMM）")
    private String postingPeriod;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @TableField("name")
    @ApiModelProperty(value = "资产名称")
    private String name;

    /** 资产类别名称 */
    @Excel(name = "资产类别名称")
    @TableField("name")
    @ApiModelProperty(value = "资产类别名称")
    private String categoryName;

    /** 资产类别ID */
    @Excel(name = "资产类别ID")
    @TableField("name")
    @ApiModelProperty(value = "资产类别id")
    private Long categoryId;

    /** 资产ID */
    @Excel(name = "资产ID")
    @TableField("asset_id")
    @ApiModelProperty(value = "资产类别ID")
    private Long assetId;

}
