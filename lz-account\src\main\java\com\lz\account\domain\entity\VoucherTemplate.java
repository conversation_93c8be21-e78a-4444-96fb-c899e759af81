package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@TableName(value = "lz_voucher_template")
@ApiModel(description = "凭证模板实体类")
public class VoucherTemplate implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    /**
     * 模板名称
     */
    @TableField(value = "name")
    @ApiModelProperty(value = "模板名称", example = "工资模板")
    private String name;

    @TableField(value = "is_default")
    @ApiModelProperty(value = "是否默认模板", example = "true")
    private Boolean isDefault;

    @TableField(value = "type")
    @ApiModelProperty(value = "模板类型", example = "1")
    private Byte type;

    @TableField(value = "account_sets_id")
    @ApiModelProperty(value = "账套ID", example = "1001")
    private Long accountSetsId;

    @TableField(value = "debit_amount")
    @ApiModelProperty(value = "借方总金额", example = "1000.0")
    private BigDecimal debitAmount;

    @TableField(value = "credit_amount")
    @ApiModelProperty(value = "贷方总金额", example = "1000.0")
    private BigDecimal creditAmount;

    @TableField(exist = false)
    @ApiModelProperty(value = "凭证模板明细列表")
    private List<VoucherTemplateDetails> details;

    private static final long serialVersionUID = 1L;

    public static final String COL_NAME = "name";

    public static final String COL_IS_DEFAULT = "is_default";

    public static final String COL_TYPE = "type";

    public static final String COL_ACCOUNT_SETS_ID = "account_sets_id";

    public static final String COL_DEBIT_AMOUNT = "debit_amount";

    public static final String COL_CREDIT_AMOUNT = "credit_amount";
}
