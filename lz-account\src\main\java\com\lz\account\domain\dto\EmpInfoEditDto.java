package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 员工薪酬和社保信息修改dto
 */
@Data
@ApiModel(description = "员工薪酬和社保信息修改dto")
public class EmpInfoEditDto {
    /**
     * ID
     */
    @ApiModelProperty(value = "员工薪酬表ID")
    private Long id;
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工信息表ID")
    private Long employeeId;
    /**
     * 奖金
     */
    @ApiModelProperty(value = "奖金")
    private BigDecimal bonus;

    /**
     * 应发工资
     */
    @ApiModelProperty(value = "应发工资")
    private BigDecimal grossSalary;
    /**
     * 免税所得
     */
    @ApiModelProperty(value = "免税所得")
    private BigDecimal taxExemptIncome;
    /**
     * 其他扣款
     */
    @ApiModelProperty(value = "其他扣款")
    private BigDecimal otherDeductions;
    /**
     * 社保信息集合
     */
    @ApiModelProperty(value = "社保信息集合")
    private List<EmpSecurityEditDto> empSecurityEditDtoList;

}
