package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @description: 公司薪酬查询dto
 */
@ApiModel(description = "公司薪酬查询dto")
@Data
public class HrSalaryQueryDto {
    @DateTimeFormat(pattern = "yyyyMM")
    @ApiModelProperty(value = "记账期间搜索开始时间")
    private String postingStartPeriod;
    @DateTimeFormat(pattern = "yyyyMM")
    @ApiModelProperty(value = "记账期间搜索结束时间")
    private String postingEndPeriod;
    @ApiModelProperty(value = "薪酬类型")
    private String salaryType;
}
