package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 报模板对象 lz_report_template
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "报模板")
@TableName(value = "lz_report_template")
@Data
public class ReportTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Long id;

    /** 报表名称 */
    @Excel(name = "报表名称")
    @TableField("name")
    @ApiModelProperty(value = "报表名称")
    private String name;

    /** 所属账套id */
    @Excel(name = "所属账套id")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "所属账套id")
    private Long accountSetsId;

    /** 模板键 */
    @Excel(name = "模板键")
    @TableField("template_key")
    @ApiModelProperty(value = "模板键")
    private String templateKey;

    /** 报表类型：0普通报表，1资产报表 */
    @Excel(name = "报表类型：0普通报表，1资产报表")
    @TableField("type")
    @ApiModelProperty(value = "报表类型：0普通报表，1资产报表")
    private Integer type;

    /** 会计准则（0.企业会计准则、1.企业会计准则、2.民间非营利组织会计制度） */
    @Excel(name = "会计准则", readConverterExp = "0.小企业会计准则、1.企业会计准则、2.民间非营利组织会计制度")
    @TableField("accounting_standards")
    @ApiModelProperty(value = "会计准则（0.小企业会计准则、1.企业会计准则、2.民间非营利组织会计制度）")
    private Integer accountingStandards;

    /** 维度 */
    @Excel(name = "维度")
    @TableField("dimensions")
    @ApiModelProperty(value = "维度")
    private Integer dimensions;

    /** 是否默认 */
    @Excel(name = "是否默认")
    @TableField("is_default")
    @ApiModelProperty(value = "是否默认")
    private Integer isDefault;

    /** 显示位置 */
    @Excel(name = "显示位置")
    @TableField("pos")
    @ApiModelProperty(value = "显示位置")
    private Integer pos;

    /** 增值税类型 */
    @Excel(name = "增值税类型")
    @TableField("vat_type")
    @ApiModelProperty(value = "增值税类型")
    private Integer vatType;

    @TableField(exist = false)
    @ApiModelProperty(value = "报表模板项列表")
    private List<ReportTemplateItems> items;
}
