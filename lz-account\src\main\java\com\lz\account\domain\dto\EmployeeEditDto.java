package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: 员工信息修改dto
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "员工信息修改dto")
@Data
public class EmployeeEditDto extends EmployeeAddDto {
    @ApiModelProperty(value = "员工ID")
    private Long id;
    /**
     * 报送状态（1:待报送;2:报送中;3:报送失败;4:报送成功）
     */
    @ApiModelProperty(value = "报送状态（1:待报送;2:报送中;3:报送失败;4:报送成功）")
    private String sbzt;

    /**
     * 身份验证状态（0:若是身份证,状态为验证中;其他证件为暂不验证;1:验证通过;2:验证不通过;4:待验证;9:同代码0处理）
     */
    @ApiModelProperty(value = "身份验证状态（0:若是身份证,状态为验证中;其他证件为暂不验证;1:验证通过;2:验证不通过;4:待验证;9:同代码0处理）")
    private String rzzt;
}
