package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lz.account.domain.entity.Voucher;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
/**
 * 生成凭证列表参数
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
public class VoucherSubjectDto extends Voucher {

    /**
     * 借方科目ID
     *
     */
    @ApiModelProperty(value = "借方科目ID", example = "2001")
    private Long debitSubjectId;

    /**
     * 贷方科目ID
     */
    @ApiModelProperty(value = "贷方科目ID", example = "2001")
    private Long creditSubjectId;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /**
     * 资产id
     */
    @ApiModelProperty(value = "资产id")
    private Long assetId;

    /**
     * 当期折旧
     */
    @ApiModelProperty(value = "当期折旧")
    private BigDecimal CurrentDepreciation;

}
