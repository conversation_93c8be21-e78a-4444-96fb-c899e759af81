package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 根据发票项目名称查找用途，根据用途查找凭证模板
 * <AUTHOR>
 * @date 2025-05-22
 */
@Data
@ApiModel("根据发票项目名称查找用途，根据用途查找凭证模板Dto")
public class ProjectTemplateDto {

    /**
     * 发票id
     */
    @TableId(value = "invoice_id")
    @ApiModelProperty(value = "发票id")
    private Long invoiceId;

    /**
     * 模板id
     */
    @TableId(value = "template_id")
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    /**
     * 模板名称
     */
    @TableId(value = "template_name")
    @ApiModelProperty(value = "模板名称")
    private String templateName;

}
