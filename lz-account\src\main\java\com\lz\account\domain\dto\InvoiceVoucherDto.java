package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lz.account.domain.entity.VoucherDetails;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 发票预览后生成凭证接口响应参数
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
@ApiModel("发票预览后生成凭证接口参数Dto")
public class InvoiceVoucherDto {
    /**
     * 发票id
     */
    @ApiModelProperty(value = "发票id", example = "95")
    private Long invoiceId;


    /**
     * 凭证编号
     */
    @TableField(value = "code")
    @ApiModelProperty(value = "凭证编号", example = "1001")
    private Integer code;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注", example = "工资发放")
    private String remark;

    /**
     * 附单据数量
     */
    @TableField(value = "receipt_num")
    @ApiModelProperty(value = "附单据数量", example = "3")
    private Integer receiptNum;


    /**
     * 凭证明细列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "凭证明细列表")
    private List<VoucherDetailsDto> details = new ArrayList<>(0);
}
