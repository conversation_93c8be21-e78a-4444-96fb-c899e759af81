package com.lz.account.domain.dto;

import com.lz.system.domain.Invoice;
import com.lz.system.domain.InvoiceProject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
/**
 * 新增和修改发票接口响应参数
 * <AUTHOR>
 * @date 2025-05-06
 */
@ApiModel("新增和修改发票Dto")
@Data
public class InvoiceInsertDto extends Invoice {
    /**
     * 发票项目列表
     */
    @ApiModelProperty(value = "发票项目列表")
    private List<InvoiceProject> invoiceProjectList;
}
