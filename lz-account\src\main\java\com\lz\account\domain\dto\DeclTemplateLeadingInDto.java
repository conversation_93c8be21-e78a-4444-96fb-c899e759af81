package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 申报模版对象 decl_template
 *
 */
@ApiModel(description = "导入，报表配置")
@Data
public class DeclTemplateLeadingInDto
{
    private static final long serialVersionUID = 1L;


    /** 税种 */
    @ApiModelProperty(value = "税种")
    private String taxesCategories;

    /** 模版名称 */
    @ApiModelProperty(value = "模版名称")
    private String templateName;

    /** 模版描述 */
    @ApiModelProperty(value = "模版描述")
    private String templateRemark;

    /** 适用地区 */
    @ApiModelProperty(value = "适用地区")
    private String applicationZone;

    /** 上传文件ID */
    @ApiModelProperty(value = "上传文件ID")
    private Long storageId;

}
