package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 申报模版对象 decl_template
 *
 */
@ApiModel(description = "报表配置配置Dto")
@Data
public class DeclTemplateDeployDto
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 模版数据 */
    @ApiModelProperty(value = "模版数据")
    private String templateData;


}
