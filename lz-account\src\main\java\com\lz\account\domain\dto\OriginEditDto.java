package com.lz.account.domain.dto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 原始凭证编辑dto
 */
@ApiModel(value = "原始凭证编辑dto")
@Data
public class OriginEditDto {
    /** 主键 */
    @ApiModelProperty(value = "主键")
    private Long id;
    /** 原始凭证文件分类表ID */
    @ApiModelProperty(value = "原始凭证文件分类表ID")
    private Long baseFinancialDocCategoryId;
    /** 金额 */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    /** 原始凭证用途表ID */
    @ApiModelProperty(value = "原始凭证用途表ID")
    private Long purposeId;

}
