package com.lz.account.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 员工社会保险明细对象 hr_social_security
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "员工社会保险明细")
@TableName(value = "hr_social_security")
@Data
public class HrSocialSecurity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 员工ID
     */
    @Excel(name = "员工ID")
    @TableField("employee_id")
    @ApiModelProperty(value = "员工ID")
    private Long employeeId;

    /**
     * 险种类型
     */
    @Excel(name = "险种类型")
    @TableField("insurance_type")
    @ApiModelProperty(value = "险种类型")
    private String insuranceType;

    /**
     * 险种名称
     */
    @Excel(name = "险种名称")
    @TableField("insurance_name")
    @ApiModelProperty(value = "险种名称")
    private String insuranceName;

    /**
     * 缴费基数
     */
    @Excel(name = "缴费基数")
    @TableField("base_amount")
    @ApiModelProperty(value = "缴费基数")
    private BigDecimal baseAmount;

    /**
     * 单位缴纳比例
     */
    @Excel(name = "单位缴纳比例")
    @TableField("company_rate")
    @ApiModelProperty(value = "单位缴纳比例")
    private BigDecimal companyRate;

    /**
     * 个人缴纳比例
     */
    @Excel(name = "个人缴纳比例")
    @TableField("personal_rate")
    @ApiModelProperty(value = "个人缴纳比例")
    private BigDecimal personalRate;

    /**
     * 单位缴纳金额
     */
    @Excel(name = "单位缴纳金额")
    @TableField(value = "company_amount", exist = false)
    @ApiModelProperty(value = "单位缴纳金额")
    private BigDecimal companyAmount;

    /**
     * 个人缴纳金额
     */
    @Excel(name = "个人缴纳金额")
    @TableField(value = "personal_amount", exist = false)
    @ApiModelProperty(value = "个人缴纳金额")
    private BigDecimal personalAmount;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("effective_date")
    @ApiModelProperty(value = "生效日期")
    private Date effectiveDate;

    /**
     * 账套ID
     */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人ID
     */
    @Excel(name = "创建人ID")
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人ID")
    private Long creatorId;

    /**
     * 创建人姓名
     */
    @Excel(name = "创建人姓名")
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人ID
     */
    @Excel(name = "更新人ID")
    @TableField(value = "updater_id", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新人ID")
    private Long updaterId;

    /**
     * 更新人姓名
     */
    @Excel(name = "更新人姓名")
    @TableField(value = "updater_name", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /**
     * 删除标识(0:正常 1:删除)
     */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识(0:正常 1:删除)")
    private String delFlag;

}
