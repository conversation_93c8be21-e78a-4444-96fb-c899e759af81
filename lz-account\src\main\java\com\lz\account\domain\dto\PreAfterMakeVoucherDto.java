package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 预览后生成凭证dto
 */
@Data
@ApiModel(value = "预览后生成凭证dto")
public class PreAfterMakeVoucherDto {

    @ApiModelProperty(value = "原始凭证ID")
    private Long originVoucherId;
    @ApiModelProperty(value = "模板id")
    private Long templateId;
    @ApiModelProperty(value = "凭证明细项")
    private List<detailsDto> details;


    @ApiModel(value = "凭证明细dto")
    @Data
    public static class detailsDto {
        @ApiModelProperty(value = "科目id")
        private Long subjectId;
        @ApiModelProperty(value = "借方金额")
        private BigDecimal debitAmount;
        @ApiModelProperty(value = "贷方金额")
        private BigDecimal creditAmount;
        @ApiModelProperty(value = "摘要", example = "工资发放")
        private String summary;
    }
}
