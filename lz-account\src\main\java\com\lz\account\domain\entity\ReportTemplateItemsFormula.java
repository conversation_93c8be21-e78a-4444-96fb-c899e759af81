package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 报模板项公式对象 lz_report_template_items_formula
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "报模板项公式")
@TableName(value = "lz_report_template_items_formula")
@Data
public class ReportTemplateItemsFormula extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Long id;

    /** 模板 id */
    @Excel(name = "模板 id")
    @TableField("template_id")
    @ApiModelProperty(value = "模板 id")
    private Long templateId;

    /** 模板项id */
    @Excel(name = "模板项id")
    @TableField("template_items_id")
    @ApiModelProperty(value = "模板项id")
    private Long templateItemsId;

    /** 所属账套id */
    @Excel(name = "所属账套id")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "所属账套id")
    private Long accountSetsId;

    /** 计算方式 */
    @Excel(name = "计算方式")
    @TableField("calculation")
    @ApiModelProperty(value = "计算方式")
    private Object calculation;

    /** 取数规则：0,净发生额度 1,借方发生额 2,贷方发生额 */
    @Excel(name = "取数规则：0,净发生额度 1,借方发生额 2,贷方发生额")
    @TableField("access_rules")
    @ApiModelProperty(value = "取数规则：0,净发生额度 1,借方发生额 2,贷方发生额")
    private Integer accessRules;

    /** 数据来源标识 */
    @Excel(name = "数据来源标识")
    @TableField("from_tag")
    @ApiModelProperty(value = "数据来源标识")
    private String fromTag;

    /** 来源 */
    @Excel(name = "来源")
    @TableField("source")
    @ApiModelProperty(value = "来源")
    private Integer source;

}
