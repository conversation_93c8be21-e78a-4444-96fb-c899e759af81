package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 凭证字对象 lz_voucher_word
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "凭证字")
@TableName(value = "lz_voucher_word")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VoucherWord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;

    /** 凭证字 */
    @Excel(name = "凭证字")
    @TableField("word")
    @ApiModelProperty(value = "凭证字")
    private String word;

    /** 打印标题 */
    @Excel(name = "打印标题")
    @TableField("print_title")
    @ApiModelProperty(value = "打印标题")
    private String printTitle;

    /** 是否默认 */
    @Excel(name = "是否默认")
    @TableField("is_default")
    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;

    /** 账套id */
    @Excel(name = "账套id")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套id")
    private Long accountSetsId;

}
