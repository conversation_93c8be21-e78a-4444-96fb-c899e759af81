package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 申报模版对象 decl_template
 *
 */
@ApiModel(description = "报表配置新增")
@Data
public class DeclTemplateInsertDto
{
    private static final long serialVersionUID = 1L;

    /** 税种 */
    @Excel(name = "税种")
    @TableField("taxes_categories")
    @ApiModelProperty(value = "税种")
    private String taxesCategories;

    /** 模版名称 */
    @Excel(name = "模版名称")
    @TableField("template_name")
    @ApiModelProperty(value = "模版名称")
    private String templateName;

    /** 模版描述 */
    @Excel(name = "模版描述")
    @TableField("template_remark")
    @ApiModelProperty(value = "模版描述")
    private String templateRemark;

    /** 适用地区 */
    @Excel(name = "适用地区")
    @TableField("application_zone")
    @ApiModelProperty(value = "适用地区")
    private String applicationZone;


}
