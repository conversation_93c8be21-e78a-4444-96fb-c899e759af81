package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lz.account.domain.entity.FaAsset;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
/**
 * 新增资产参数
 * <AUTHOR>
 * @date 2025-04-19
 */
@Data
@ApiModel("新增资产Dto")
public class FaAssetInsertDto {
    /** 资产名称 */
    @Excel(name = "资产名称")
    @TableField("name")
    @ApiModelProperty(value = "资产名称")
    private String name;

    /** 资产类别ID */
    @Excel(name = "资产类别ID")
    @TableField("category_id")
    @ApiModelProperty(value = "资产类别ID")
    private Long categoryId;

    /** 原值 */
    @Excel(name = "原值")
    @TableField("original_value")
    @ApiModelProperty(value = "原值")
    private BigDecimal originalValue;

    /** 存放位置 */
    @Excel(name = "存放位置")
    @TableField("location")
    @ApiModelProperty(value = "存放位置")
    private String location;

    /** 数量 */
    @Excel(name = "数量")
    @TableField("quantity")
    @ApiModelProperty(value = "数量")
    private Long quantity;

    /** 净残值率（%） */
    @Excel(name = "净残值率", readConverterExp = "%=")
    @TableField("net_salvage_rate")
    @ApiModelProperty(value = "净残值率（%）")
    private BigDecimal netSalvageRate;

    /** 平均年限法 */
    @Excel(name = "平均年限法")
    @TableField("depreciation_method")
    @ApiModelProperty(value = "平均年限法")
    private String depreciationMethod;

    /** 开始使用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始使用日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("start_use_date")
    @ApiModelProperty(value = "开始使用日期")
    private Date startUseDate;

    /** 开始计提期间（YYYYMM） */
    @Excel(name = "开始计提期间", readConverterExp = "Y=YYYMM")
    @TableField("start_depreciation_period")
    @ApiModelProperty(value = "开始计提期间（YYYYMM）")
    private String startDepreciationPeriod;

    /** 固定资产科目ID */
    @Excel(name = "固定资产科目ID")
    @TableField("fixed_asset_subject_id")
    @ApiModelProperty(value = "固定资产科目ID")
    private Long fixedAssetSubjectId;

    /** 应付账款科目ID */
    @Excel(name = "应付账款科目ID")
    @TableField("purchase_subject_id")
    @ApiModelProperty(value = "应付账款科目ID")
    private Long purchaseSubjectId;

    /** 累计折旧科目ID */
    @Excel(name = "累计折旧科目ID")
    @TableField("accumulated_depr_subject_id")
    @ApiModelProperty(value = "累计折旧科目ID")
    private Long accumulatedDeprSubjectId;

    /** 折旧费用科目ID */
    @Excel(name = "折旧费用科目ID")
    @TableField("depreciation_expense_subject_id")
    @ApiModelProperty(value = "折旧费用科目ID")
    private Long depreciationExpenseSubjectId;

    /** 固定资产清理科目ID */
    @Excel(name = "固定资产清理科目ID")
    @TableField("asset_clean_subject_id")
    @ApiModelProperty(value = "固定资产清理科目ID")
    private Long assetCleanSubjectId;

    /** 最终处置-出售科目ID */
    @Excel(name = "最终处置-出售科目ID")
    @TableField("final_sale_subject_id")
    @ApiModelProperty(value = "最终处置-出售科目ID")
    private Long finalSaleSubjectId;

    /** 最终处置-盘亏科目ID */
    @Excel(name = "最终处置-盘亏科目ID")
    @TableField("final_loss_subject_id")
    @ApiModelProperty(value = "最终处置-盘亏科目ID")
    private Long finalLossSubjectId;

    /** 最终处置-其他科目ID */
    @Excel(name = "最终处置-其他科目ID")
    @TableField("final_other_subject_id")
    @ApiModelProperty(value = "最终处置-其他科目ID")
    private Long finalOtherSubjectId;
}
