package com.lz.account.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 原始凭证模版对象 lz_invoice_voucher_template
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "原始凭证模版")
@TableName(value = "lz_invoice_voucher_template")
@Data
public class InvoiceVoucherTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 原始凭证用途表ID（允许多选，以英文逗号分隔） */
    @Excel(name = "原始凭证用途表ID", readConverterExp = "允=许多选，以英文逗号分隔")
    @TableField("purpose_id")
    @ApiModelProperty(value = "原始凭证用途表ID（允许多选，以英文逗号分隔）")
    private String purposeId;

    /** 模板名称 */
    @Excel(name = "模板名称")
    @TableField("name")
    @ApiModelProperty(value = "模板名称")
    private String name;

    /** 模版分类（1：销项模版，2：其他模版） */
    @Excel(name = "模版分类", readConverterExp = "1=：销项模版，2：其他模版")
    @TableField("category")
    @ApiModelProperty(value = "模版分类（1：销项模版，2：其他模版）")
    private String category;

    /** 匹配字词 */
    @Excel(name = "匹配字词")
    @TableField("match_words")
    @ApiModelProperty(value = "匹配字词")
    private String matchWords;

    /** 是否默认（0：否，1：是） */
    @Excel(name = "是否默认", readConverterExp = "0=：否，1：是")
    @TableField("is_default")
    @ApiModelProperty(value = "是否默认（0：否，1：是）")
    private Long isDefault;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0:未删除 1:已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0:未删除 1:已删除）")
    private String delFlag;

}
