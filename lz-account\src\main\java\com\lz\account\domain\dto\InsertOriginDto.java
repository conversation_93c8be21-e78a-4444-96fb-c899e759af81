package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 上传文件后向原始凭证新增记录dto
 */
@ApiModel(description = "上传文件后向原始凭证新增记录dto")
@Data
public class InsertOriginDto {
    @ApiModelProperty(value = "文件存储id")
    private Long storageId;
    @ApiModelProperty(value = "原始凭证用途id")
    private Long purposeId;
    @ApiModelProperty(value = "文件分类id")
    private Long docCategoryId;
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
}
