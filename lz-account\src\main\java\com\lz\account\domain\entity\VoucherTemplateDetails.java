package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName(value = "lz_voucher_template_details")
@ApiModel(description = "凭证模板明细实体类")
public class VoucherTemplateDetails implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField(value = "voucher_template_id")
    @ApiModelProperty(value = "凭证模板ID", example = "1001")
    private Long voucherTemplateId;

    /**
     * 摘要
     */
    @TableField(value = "summary")
    @ApiModelProperty(value = "摘要", example = "工资发放")
    private String summary;

    @TableField(value = "subject_id")
    @ApiModelProperty(value = "科目ID", example = "2001")
    private Long subjectId;

    /**
     * 科目名称
     */
    @TableField(value = "subject_name")
    @ApiModelProperty(value = "科目名称", example = "现金")
    private String subjectName;

    /**
     * 借方金额
     */
    @TableField(value = "debit_amount")
    @ApiModelProperty(value = "借方金额", example = "1000.0")
    private BigDecimal debitAmount;

    /**
     * 贷方金额
     */
    @TableField(value = "credit_amount")
    @ApiModelProperty(value = "贷方金额", example = "1000.0")
    private BigDecimal creditAmount;

    @TableField(value = "account_sets_id")
    @ApiModelProperty(value = "账套ID", example = "1001")
    private Long accountSetsId;

    @TableField(value = "subject_code")
    @ApiModelProperty(value = "科目编码", example = "1001")
    private String subjectCode;

    @TableField(value = "auxiliary_title")
    @ApiModelProperty(value = "辅助名称", example = "部门A")
    private String auxiliaryTitle;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_VOUCHER_TEMPLATE_ID = "voucher_template_id";

    public static final String COL_SUMMARY = "summary";

    public static final String COL_SUBJECT_ID = "subject_id";

    public static final String COL_SUBJECT_NAME = "subject_name";

    public static final String COL_DEBIT_AMOUNT = "debit_amount";

    public static final String COL_CREDIT_AMOUNT = "credit_amount";

    public static final String COL_ACCOUNT_SETS_ID = "account_sets_id";

    public static final String COL_SUBJECT_CODE = "subject_code";

    public static final String COL_AUXILIARY_TITLE = "auxiliary_title";
}
