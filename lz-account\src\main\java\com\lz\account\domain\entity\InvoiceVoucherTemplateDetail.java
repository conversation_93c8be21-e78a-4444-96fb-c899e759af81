package com.lz.account.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 原始凭证模版明细对象 lz_invoice_voucher_template_detail
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "原始凭证模版明细")
@TableName(value = "lz_invoice_voucher_template_detail")
@Data
public class InvoiceVoucherTemplateDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 原始凭证模版表ID */
    @Excel(name = "原始凭证模版表ID")
    @TableField("invoice_voucher_template_id")
    @ApiModelProperty(value = "原始凭证模版表ID")
    private Long invoiceVoucherTemplateId;

    /** 摘要 */
    @Excel(name = "摘要")
    @TableField("abstract_text")
    @ApiModelProperty(value = "摘要")
    private String abstractText;

    /** 科目ID */
    @Excel(name = "科目ID")
    @TableField("subject_id")
    @ApiModelProperty(value = "科目ID")
    private Long subjectId;

    /** 科目编码 */
    @Excel(name = "科目编码")
    @TableField(exist = false)
    @ApiModelProperty(value = "科目编码")
    private String subjectCode;

    /** 科目名称 */
    @Excel(name = "科目名称")
    @TableField(exist = false)
    @ApiModelProperty(value = "科目名称")
    private String subjectName;

    /** 方向（1：借，2：贷） */
    @Excel(name = "方向", readConverterExp = "1=：借，2：贷")
    @TableField("direction")
    @ApiModelProperty(value = "方向（1：借，2：贷）")
    private String direction;

    /** 取值（0：价税合计，1：税额，2：不含税金额） */
    @Excel(name = "取值", readConverterExp = "0=：价税合计，1：税额，2：不含税金额")
    @TableField("value_type")
    @ApiModelProperty(value = "取值（0：价税合计，1：税额，2：不含税金额）")
    private String valueType;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0:未删除 1:已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0:未删除 1:已删除）")
    private String delFlag;

}
