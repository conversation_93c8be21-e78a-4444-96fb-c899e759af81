package com.lz.account.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lz.account.domain.entity.InvoiceVoucherTemplateDetail;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 原始凭证模版对象 lz_invoice_voucher_template
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
public class InvoiceVoucherTemplateDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** ID */
    @Excel(name = "ID")
    @ApiModelProperty(value = "模板ID")
    private Long id;

    /** 原始凭证用途表ID（允许多选，以英文逗号分隔） */
    @Excel(name = "原始凭证用途表ID", readConverterExp = "多选用英文逗号分隔")
    @ApiModelProperty(value = "原始凭证用途ID，多个以英文逗号分隔")
    private String purposeId;

    /** 模板名称 */
    @Excel(name = "模板名称")
    @ApiModelProperty(value = "模板名称")
    private String name;

    /** 模板分类（1=销项模板，2=进项模板，3=费用小票模板，4=无票收入模板） */
    @Excel(name = "模板分类", readConverterExp = "模板分类（1=销项模板，2=进项模板，3=费用小票模板，4=无票收入模板）")
    @ApiModelProperty(value = "模板分类（1=销项模板，2=进项模板，3=费用小票模板，4=无票收入模板）")
    private String category;

    /** 是否默认（0：否，1：是） */
    @Excel(name = "是否默认", readConverterExp = "0=否,1=是")
    @ApiModelProperty(value = "是否为默认模板（0=否，1=是）")
    private Long isDefault;

    /** 匹配字词 */
    @Excel(name = "匹配字词")
    @ApiModelProperty(value = "匹配字词")
    private List<String> matchWords;

    /** 模板明细列表 */
    @ApiModelProperty(value = "模板明细列表")
    private List<InvoiceVoucherTemplateDetail> details;

}
