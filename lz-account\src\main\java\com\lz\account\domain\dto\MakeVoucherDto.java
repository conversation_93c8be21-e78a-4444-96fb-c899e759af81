package com.lz.account.domain.dto;

import com.lz.account.domain.entity.Voucher;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: 生成凭证dto
 */
@Data
@ApiModel(description = "生成凭证dto")
public class MakeVoucherDto {

    @ApiModelProperty(value = "原始凭证id")
    private Long id;
    @ApiModelProperty(value = "凭证模板id")
    private Long templateId;

}
