package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 授权接收参数dto
 * <AUTHOR>
 * @date 2025-06-14
 */
@Data
@ApiModel(value = "授权接收参数dto")
public class LicenseRoleDto {
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 用户手机号
     */
    @ApiModelProperty(value = "用户手机号列表")
    private List<Long> phone;

    /**
     * 权限字符
     */
    @ApiModelProperty(value = "权限字符")
    private String roleKey;

    /**
     * 账套id
     */
    @ApiModelProperty(value = "账套id")
    private Long accountSetId;
}
