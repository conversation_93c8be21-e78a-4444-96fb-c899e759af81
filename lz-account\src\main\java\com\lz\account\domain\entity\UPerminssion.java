package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 用账关联对象 lz_user_account_sets
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@ApiModel(description = "用账关联")
@TableName(value = "lz_user_account_sets")
@Data
public class UPerminssion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Long id;

    /** 账套id */
    @Excel(name = "账套id")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套id")
    private Long accountSetsId;

    /** 用户id */
    @Excel(name = "用户id")
    @TableField("user_id")
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /** 权限字符 */
    @Excel(name = "权限字符")
    @TableField("perms")
    @ApiModelProperty(value = "权限字符")
    private String perms;

}
