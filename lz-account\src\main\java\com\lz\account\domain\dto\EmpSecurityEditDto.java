package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 员工社保休息修改dto
 */
@Data
@ApiModel(description = "员工社保修改dto")
public class EmpSecurityEditDto {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "社保信息表ID")
    private Long id;
    /**
     * 险种类型
     */
    @ApiModelProperty(value = "险种类型")
    private String insuranceType;

    /**
     * 险种名称
     */
    @ApiModelProperty(value = "险种名称")
    private String insuranceName;

    /**
     * 缴费基数
     */
    @ApiModelProperty(value = "缴费基数")
    private BigDecimal baseAmount;

    /**
     * 单位缴纳比例
     */
    @ApiModelProperty(value = "单位缴纳比例")
    private BigDecimal companyRate;

    /**
     * 个人缴纳比例
     */
    @ApiModelProperty(value = "个人缴纳比例")
    private BigDecimal personalRate;

}
