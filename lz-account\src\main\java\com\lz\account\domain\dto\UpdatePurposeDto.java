package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
/**、
 * 修改用途接口响应参数
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ApiModel("修改用途Dto")
public class UpdatePurposeDto {
    /** 用途ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "用途ID")
    private Long id;

    /** 父级ID（为空表示一级分类） */
    @Excel(name = "父级ID", readConverterExp = "为=空表示一级分类")
    @TableField("parent_id")
    @ApiModelProperty(value = "父级ID（为空表示一级分类）")
    private Long parentId;

    /** 用途名称 */
    @Excel(name = "用途名称")
    @TableField("name")
    @ApiModelProperty(value = "用途名称")
    private String name;

    /** 匹配字词（允许多个，英文逗号分隔） */
    @Excel(name = "匹配字词", readConverterExp = "允许多个，英文逗号分隔")
    @TableField("match_words")
    @ApiModelProperty(value = "匹配字词（允许多个，英文逗号分隔）")
    private List<String> matchWords;

    /** 匹配顺序（0~99 优先匹配最大的） */
    @Excel(name = "匹配顺序", readConverterExp = "0~99 优先匹配最大的")
    @TableField("match_order")
    @ApiModelProperty(value = "匹配顺序（0~99 优先匹配最大的）")
    private String matchOrder;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;
}
