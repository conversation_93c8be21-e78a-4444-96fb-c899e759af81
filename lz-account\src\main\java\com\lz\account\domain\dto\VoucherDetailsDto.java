package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 凭证明细所需参数
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
public class VoucherDetailsDto {
    /**
     * 摘要
     */
    @TableField(value = "summary")
    @ApiModelProperty(value = "摘要", example = "工资发放")
    private String summary;

    /**
     * 科目ID
     */
    @TableField(value = "subject_id")
    @ApiModelProperty(value = "科目ID", example = "2001")
    private Long subjectId;

    /**
     * 科目名称
     */
    @TableField(value = "subject_name")
    @ApiModelProperty(value = "科目名称", example = "现金")
    private String subjectName;

    /**
     * 科目编码
     */
    @TableField(value = "subject_code")
    @ApiModelProperty(value = "科目编码", example = "1001")
    private String subjectCode;

    /**
     * 借方金额
     */
    @TableField(value = "debit_amount")
    @ApiModelProperty(value = "借方金额", example = "1000.0")
    private BigDecimal debitAmount;

    /**
     * 贷方金额
     */
    @TableField(value = "credit_amount")
    @ApiModelProperty(value = "贷方金额", example = "1000.0")
    private BigDecimal creditAmount;


}
