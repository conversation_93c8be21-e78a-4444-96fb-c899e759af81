package com.lz.account.domain.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 记账凭证明细对象 lz_voucher_details
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "记账凭证明细")
@TableName(value = "lz_voucher_details")
@Data
public class VoucherDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;

    /** 凭证id */
    @Excel(name = "凭证id")
    @TableField("voucher_id")
    @ApiModelProperty(value = "凭证id")
    private Long voucherId;

    /** 摘要 */
    @Excel(name = "摘要")
    @TableField("summary")
    @ApiModelProperty(value = "摘要")
    private String summary;

    /** 所属科目id */
    @Excel(name = "所属科目id")
    @TableField("subject_id")
    @ApiModelProperty(value = "所属科目id")
    private Long subjectId;

    /** 科目名称 */
    @Excel(name = "科目名称")
    @TableField("subject_name")
    @ApiModelProperty(value = "科目名称")
    private String subjectName;

    /** 科目编码 */
    @Excel(name = "科目编码")
    @TableField("subject_code")
    @ApiModelProperty(value = "科目编码")
    private String subjectCode;

    /** 借方金额 */
    @Excel(name = "借方金额")
    @TableField("debit_amount")
    @ApiModelProperty(value = "借方金额")
    private BigDecimal debitAmount;

    /** 贷方金额 */
    @Excel(name = "贷方金额")
    @TableField("credit_amount")
    @ApiModelProperty(value = "贷方金额")
    private BigDecimal creditAmount;

    /** 辅助名称 */
    @Excel(name = "辅助名称")
    @TableField("auxiliary_title")
    @ApiModelProperty(value = "辅助名称")
    private String auxiliaryTitle;

    /** 数量 */
    @Excel(name = "数量")
    @TableField("num")
    @ApiModelProperty(value = "数量")
    private Integer num;

    /** 单价 */
    @Excel(name = "单价")
    @TableField("price")
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /** 所属账套id */
    @Excel(name = "所属账套id")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "所属账套id")
    private Long accountSetsId;

    /** 期初累计借方 */
    @Excel(name = "期初累计借方")
    @TableField("cumulative_debit")
    @ApiModelProperty(value = "期初累计借方")
    private BigDecimal cumulativeDebit;

    /** 期初累计贷方 */
    @Excel(name = "期初累计贷方")
    @TableField("cumulative_credit")
    @ApiModelProperty(value = "期初累计贷方")
    private BigDecimal cumulativeCredit;

    /** 期初累计借方数量 */
    @Excel(name = "期初累计借方数量")
    @TableField("cumulative_debit_num")
    @ApiModelProperty(value = "期初累计借方数量")
    private Integer cumulativeDebitNum;

    /** 期初累计贷方数量 */
    @Excel(name = "期初累计贷方数量")
    @TableField("cumulative_credit_num")
    @ApiModelProperty(value = "期初累计贷方数量")
    private Integer cumulativeCreditNum;

    /** 是否结转损益 */
    @Excel(name = "是否结转损益")
    @TableField("carry_forward")
    @ApiModelProperty(value = "是否结转损益")
    private Boolean carryForward;

    /** 币别id */
    @Excel(name = "币别id")
    @TableField("currency_id")
    @ApiModelProperty(value = "币别id")
    private Integer currencyId;

    /** 币别名称 */
    @Excel(name = "币别名称")
    @TableField("currency_name")
    @ApiModelProperty(value = "币别名称")
    private String currencyName;

    /** 汇率 */
    @Excel(name = "汇率")
    @TableField("exchange_rate")
    @ApiModelProperty(value = "汇率")
    private Double exchangeRate;

    /** 原始金额 */
    @Excel(name = "原始金额")
    @TableField("original_amount")
    @ApiModelProperty(value = "原始金额")
    private BigDecimal originalAmount;

    /** 凭证日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "凭证日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("voucher_date")
    @ApiModelProperty(value = "凭证日期")
    private Date voucherDate;

    @TableField(exist = false)
    @ApiModelProperty(value = "辅助核算详情列表")
    private List<AccountingCategoryDetails> auxiliary = new ArrayList<>(0);

    @TableField(exist = false)
    @ApiModelProperty(value = "科目详情")
    private Subject subject;
}
