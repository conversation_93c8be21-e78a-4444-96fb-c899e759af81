package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 报模板项对象 lz_report_template_items
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "报模板项")
@TableName(value = "lz_report_template_items")
@Data
public class ReportTemplateItems extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Long id;

    /** 模板id */
    @Excel(name = "模板id")
    @TableField("template_id")
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    /** 标题 */
    @Excel(name = "标题")
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /** 父项ID */
    @Excel(name = "父项ID")
    @TableField("parent_id")
    @ApiModelProperty(value = "父项ID")
    private Long parentId;

    /** 行次 */
    @Excel(name = "行次")
    @TableField("line_num")
    @ApiModelProperty(value = "行次")
    private Integer lineNum;

    /** 资产负载类型时需要设置
0,资产 1,负债 2，所有者权益 */
    @Excel(name = "资产负载类型时需要设置 0,资产 1,负债 2，所有者权益")
    @TableField("type")
    @ApiModelProperty(value = "资产负载类型时需要设置 0,资产 1,负债 2，所有者权益")
    private Integer type;

    /** 取数来原:0,表外公式,1,表内公式 */
    @Excel(name = "取数来原:0,表外公式,1,表内公式")
    @TableField("sources")
    @ApiModelProperty(value = "取数来原:0,表外公式,1,表内公式")
    private Integer sources;

    /** 层级 */
    @Excel(name = "层级")
    @TableField("level")
    @ApiModelProperty(value = "层级")
    private Integer level;

    /** 是否加粗标题 */
    @Excel(name = "是否加粗标题")
    @TableField("is_bolder")
    @ApiModelProperty(value = "是否加粗标题")
    private Boolean isBolder;

    /** 是否可以折叠 */
    @Excel(name = "是否可以折叠")
    @TableField("is_folding")
    @ApiModelProperty(value = "是否可以折叠")
    private Boolean isFolding;

    /** 是否是归类项，归类项没有行号 */
    @Excel(name = "是否是归类项，归类项没有行号")
    @TableField("is_classified")
    @ApiModelProperty(value = "是否是归类项，归类项没有行号")
    private Boolean isClassified;

    /** 显示位置 */
    @Excel(name = "显示位置")
    @TableField("pos")
    @ApiModelProperty(value = "显示位置")
    private Integer pos;

    @TableField(exist = false)
    @ApiModelProperty(value = "公式列表")
    private List<ReportTemplateItemsFormula> formulas;

    @TableField(exist = false)
    @ApiModelProperty(value = "子项列表")
    private List<ReportTemplateItems> children;
}
