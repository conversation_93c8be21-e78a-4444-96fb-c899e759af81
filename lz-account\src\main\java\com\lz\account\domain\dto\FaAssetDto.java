package com.lz.account.domain.dto;

import com.lz.account.domain.entity.FaAsset;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 获取有效资产Dto
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@ApiModel(description = "获取有效资产Dto")
public class FaAssetDto extends FaAsset {

    /**
     * 折旧年限
     */
    @ApiModelProperty(value = "折旧年限")
    private Integer depreciation_years;
}
