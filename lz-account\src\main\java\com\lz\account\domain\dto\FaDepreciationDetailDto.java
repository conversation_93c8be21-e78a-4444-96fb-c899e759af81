package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lz.account.domain.entity.FaDepreciationDetail;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
/**
 * 折旧明细返回参数
 * <AUTHOR>
 * @date 2025-04-21
 */
@Data
@ApiModel("折旧明细表数据库返回值Dto")
public class FaDepreciationDetailDto {
    /** 类别名称 */
    @Excel(name = "类别名称")
    @TableField("name")
    @ApiModelProperty(value = "类别名称")
    private String categoryName;
    /** 资产编号（3位类别+5位序列） */
    @Excel(name = "资产编号", readConverterExp = "3=位类别+5位序列")
    @TableField("asset_number")
    @ApiModelProperty(value = "资产编号（3位类别+5位序列）")
    private String assetNumber;
    /** 资产名称 */
    @Excel(name = "资产名称")
    @TableField("name")
    @ApiModelProperty(value = "资产名称")
    private String name;
    /** 原值 */
    @Excel(name = "原值")
    @TableField("original_value")
    @ApiModelProperty(value = "原值")
    private BigDecimal originalValue;
    /** 记账期间（YYYYMM） */
    @Excel(name = "记账期间", readConverterExp = "Y=YYYMM")
    @TableField("posting_period")
    @ApiModelProperty(value = "记账期间（YYYYMM）")
    private String postingPeriod;
    /** 期初累计折旧 */
    @Excel(name = "期初累计折旧")
    @TableField("beginning_accumulated")
    @ApiModelProperty(value = "期初累计折旧")
    private BigDecimal beginningAccumulated;

    /** 当期折旧 */
    @Excel(name = "当期折旧")
    @TableField("current_depreciation")
    @ApiModelProperty(value = "当期折旧")
    private BigDecimal currentDepreciation;

    /** 本年累计折旧 */
    @Excel(name = "本年累计折旧")
    @TableField("year_to_date_depr")
    @ApiModelProperty(value = "本年累计折旧")
    private BigDecimal yearToDateDepr;

    /** 期末累计折旧 */
    @Excel(name = "期末累计折旧")
    @TableField("ending_accumulated")
    @ApiModelProperty(value = "期末累计折旧")
    private BigDecimal endingAccumulated;

    /** 期末净值 */
    @Excel(name = "期末净值")
    @TableField("ending_net_value")
    @ApiModelProperty(value = "期末净值")
    private BigDecimal endingNetValue;



}
