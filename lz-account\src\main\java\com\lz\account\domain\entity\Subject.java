package com.lz.account.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 科目对象 lz_subject
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@ApiModel(description = "科目")
@TableName(value = "lz_subject")
@Data
public class Subject extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;

    /** 科目类型 */
    @Excel(name = "科目类型")
    @TableField("type")
    @ApiModelProperty(value = "科目类型")
    private Object type;

    /** 科目编码 */
    @Excel(name = "科目编码")
    @TableField("code")
    @ApiModelProperty(value = "科目编码")
    private String code;

    /** 科目名称 */
    @Excel(name = "科目名称")
    @TableField("name")
    @ApiModelProperty(value = "科目名称")
    private String name;

    /** 助记码 */
    @Excel(name = "助记码")
    @TableField("mnemonic_code")
    @ApiModelProperty(value = "助记码")
    private String mnemonicCode;

    /** 余额方向 */
    @Excel(name = "余额方向")
    @TableField("balance_direction")
    @ApiModelProperty(value = "余额方向")
    private Object balanceDirection;

    /** 状态 */
    @Excel(name = "状态")
    @TableField("status")
    @ApiModelProperty(value = "状态")
    private Boolean status;

    /** 上级科目 */
    @Excel(name = "上级科目")
    @TableField("parent_id")
    @ApiModelProperty(value = "上级科目")
    private Long parentId;

    /** 所在级别 */
    @Excel(name = "所在级别")
    @TableField("level")
    @ApiModelProperty(value = "所在级别")
    private Short level;

    /** 是否为系统默认 */
    @Excel(name = "是否为系统默认")
    @TableField("system_default")
    @ApiModelProperty(value = "是否为系统默认")
    private Boolean systemDefault;

    /** 所属账套id */
    @Excel(name = "所属账套id")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "所属账套id")
    private Long accountSetsId;

    /** 科目余额 */
    @Excel(name = "科目余额")
    @TableField("balance")
    @ApiModelProperty(value = "科目余额")
    private BigDecimal balance;

    /** 单位 */
    @Excel(name = "单位")
    @TableField("unit")
    @ApiModelProperty(value = "单位")
    private String unit;

    /** 辅助核算 */
    @Excel(name = "辅助核算")
    @TableField("auxiliary_accounting")
    @ApiModelProperty(value = "辅助核算")
    private String auxiliaryAccounting;

    /** 币别核算 */
    @Excel(name = "币别核算")
    @TableField("currency_accounting")
    @ApiModelProperty(value = "币别核算")
    private String currencyAccounting;

    /** 上级科目编码 */
    @Excel(name = "上级科目编码")
    @TableField("parent_code")
    @ApiModelProperty(value = "上级科目编码")
    private String parentCode;

}
