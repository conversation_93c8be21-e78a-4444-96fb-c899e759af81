package com.lz.account.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 预览凭证参数
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@ApiModel("预览流水凭证Dto")
public class PreViewStatementVoucherDto {

    /**
     * 流水明细id
     */
    @ApiModelProperty(value = "流水明细id")
    private Long detailId;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    private Long templateId;

}
