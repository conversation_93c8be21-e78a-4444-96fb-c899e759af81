package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lz.account.domain.entity.FaAsset;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
/**
 * 修改资产参数
 * <AUTHOR>
 * @date 2025-04-21
 */
@Data
@ApiModel("修改资产Dto")
public class FaAssetUpdateDto {

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @TableField("name")
    @ApiModelProperty(value = "资产名称")
    private String name;

    /** 存放位置 */
    @Excel(name = "存放位置")
    @TableField("location")
    @ApiModelProperty(value = "存放位置")
    private String location;

    /** 使用状态（0：未使用，1：正常使用） */
    @Excel(name = "使用状态", readConverterExp = "0=：未使用，1：正常使用")
    @TableField("active_status")
    @ApiModelProperty(value = "使用状态（0：未使用，1：正常使用）")
    private String activeStatus;

    /** 数量 */
    @Excel(name = "数量")
    @TableField("quantity")
    @ApiModelProperty(value = "数量")
    private Long quantity;

    /** 开始使用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始使用日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("start_use_date")
    @ApiModelProperty(value = "开始使用日期")
    private Date startUseDate;

    /** 开始计提期间（YYYYMM） */
    @Excel(name = "开始计提期间", readConverterExp = "Y=YYYMM")
    @TableField("start_depreciation_period")
    @ApiModelProperty(value = "开始计提期间（YYYYMM）")
    private String startDepreciationPeriod;

    /** 预计可使用期间 */
    @Excel(name = "预计可使用期间")
    @TableField("expected_usable_periods")
    @ApiModelProperty(value = "预计可使用期间")
    private Long expectedUsablePeriods;

    /** 固定资产科目ID */
    @Excel(name = "固定资产科目ID")
    @TableField("fixed_asset_subject_id")
    @ApiModelProperty(value = "固定资产科目ID")
    private Long fixedAssetSubjectId;

    /** 应付账款科目ID */
    @Excel(name = "应付账款科目ID")
    @TableField("purchase_subject_id")
    @ApiModelProperty(value = "应付账款科目ID")
    private Long purchaseSubjectId;

    /** 累计折旧科目ID */
    @Excel(name = "累计折旧科目ID")
    @TableField("accumulated_depr_subject_id")
    @ApiModelProperty(value = "累计折旧科目ID")
    private Long accumulatedDeprSubjectId;

    /** 折旧费用科目ID */
    @Excel(name = "折旧费用科目ID")
    @TableField("depreciation_expense_subject_id")
    @ApiModelProperty(value = "折旧费用科目ID")
    private Long depreciationExpenseSubjectId;

}
