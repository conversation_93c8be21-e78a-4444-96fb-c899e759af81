package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lz.common.annotation.Excel;
import com.lz.system.domain.Invoice;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 小程序端修改发票接口响应参数
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ApiModel("小程序端修改发票Dto")
public class UniAppInvoiceUpdateDto {
    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;

    /** 原始凭证用途表ID */
    @Excel(name = "原始凭证用途表ID")
    @TableField("purpose_id")
    @ApiModelProperty(value = "原始凭证用途表ID")
    private Long purposeId;


    /** 备注 */
    @Excel(name = "备注")
    @TableField("bz")
    @ApiModelProperty(value = "备注")
    private String bz;
}
