package com.lz.account.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lz.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 最终处置接口响应参数
 * <AUTHOR>
 * @date 2025-04-27
 */
@Data
public class FinalDisposalDto {
    /** ID */
    @TableId(value = "ids", type = IdType.AUTO)
    @ApiModelProperty(value = "资产id数组")
    private Long[] ids;

    /** 最终处置方式 */
    @ApiModelProperty(value = "最终处置方式")
    private String finalDisposalMethod;

}
