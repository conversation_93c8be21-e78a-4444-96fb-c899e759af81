package com.lz.account.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lz.common.annotation.Excel;
import com.lz.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 员工薪酬对象 hr_salary_item
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@ApiModel(description = "员工薪酬")
@TableName(value = "hr_salary_item")
@Data
public class HrSalaryItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 员工ID */
    @Excel(name = "员工ID")
    @TableField("employee_id")
    @ApiModelProperty(value = "员工ID")
    private Long employeeId;

    /** 应发工资 */
    @Excel(name = "应发工资")
    @TableField("gross_salary")
    @ApiModelProperty(value = "应发工资")
    private BigDecimal grossSalary;

    /** 免税所得 */
    @Excel(name = "免税所得")
    @TableField("tax_exempt_income")
    @ApiModelProperty(value = "免税所得")
    private BigDecimal taxExemptIncome;

    /** 其他扣款 */
    @Excel(name = "其他扣款")
    @TableField("other_deductions")
    @ApiModelProperty(value = "其他扣款")
    private BigDecimal otherDeductions;

    /** 奖金 */
    @Excel(name = "奖金")
    @TableField("bonus")
    @ApiModelProperty(value = "奖金")
    private BigDecimal bonus;

    /** 应补税额 */
    @Excel(name = "应补税额")
    @TableField("tax_amount")
    @ApiModelProperty(value = "应补税额")
    private BigDecimal taxAmount;

    /** 实发工资 */
    @Excel(name = "实发工资")
    @TableField("net_salary")
    @ApiModelProperty(value = "实发工资")
    private BigDecimal netSalary;

    /** 是否直接按照6万元扣除（0:否;1:是） */
    @Excel(name = "是否直接按照6万元扣除", readConverterExp = "0=:否;1:是")
    @TableField("deduct_60k_flag")
    @ApiModelProperty(value = "是否直接按照6万元扣除（0:否;1:是）")
    private String deduct60kFlag;

    /** 是否直接按照6万元扣除确认状态（0:待确认;1:确认中;2:确认成功;3:确认失败 */
    @Excel(name = "是否直接按照6万元扣除确认状态", readConverterExp = "是否直接按照6万元扣除确认状态（0:待确认;1:确认中;2:确认成功;3:确认失败")
    @TableField("deduct_confirm_status")
    @ApiModelProperty(value = "是否直接按照6万元扣除确认状态（0:待确认;1:确认中;2:确认成功;3:确认失败")
    private String deductConfirmStatus;

    /** 确认失败原因 */
    @Excel(name = "确认失败原因")
    @TableField("deduct_confirm_reason")
    @ApiModelProperty(value = "确认失败原因")
    private String deductConfirmReason;

    /** 账套ID */
    @Excel(name = "账套ID")
    @TableField("account_sets_id")
    @ApiModelProperty(value = "账套ID")
    private Long accountSetsId;

    /** 创建时间 */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 创建人id */
    @Excel(name = "创建人id")
    @TableField("creator_id")
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    @TableField("creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /** 更新时间 */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 更新人id */
    @Excel(name = "更新人id")
    @TableField("updater_id")
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /** 更新人姓名 */
    @Excel(name = "更新人姓名")
    @TableField("updater_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /** 删除标识（0:未删除 1:已删除） */
    @TableLogic
    @TableField("del_flag")
    @ApiModelProperty(value = "删除标识（0:未删除 1:已删除）")
    private String delFlag;

}
